name: fxerp

#volumes:
#  mine_redis_data:
#  mine_mysql_data:

services:
#  redis:
#    image: redis:7.2-alpine
#    ports:
#      - "6379:6379"
#    volumes:
#      - mine_redis_data:/data
#    command: redis-server --appendonly yes
#    deploy:
#      resources:
#        limits:
#          memory: 1G
#    healthcheck:
#      test: ["CMD", "redis-cli", "ping"]
#      interval: 10s
#      timeout: 5s
#      retries: 3
#    environment:
#      - TZ=Asia/Shanghai
#
#  mysql:
#    image: mysql:5.7
#    volumes:
#      - mine_mysql_data:/var/lib/mysql
#    ports:
#      - "3306:3306"
#    command: --default-authentication-plugin=mysql_native_password
#    environment:
#      MYSQL_ROOT_PASSWORD: root
#      MYSQL_DATABASE: mineadmin
#      MYSQL_CHARACTER_SET_SERVER: utf8mb4
#      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
#      TZ: Asia/Shanghai
  hyperf:
    image: hyperf/hyperf:8.1-alpine-v3.18-swoole
    volumes:
      - ./:/www
    working_dir: /www
    ports:
      - "${HYPERF_HOST_PORT:-9501}:9501"
      - "${HYPERF_SWAGGER_PORT:-9503}:9503"
    environment:
      - TZ=Asia/Shanghai
      - APP_NAME=fxerp
    command: sh -c "ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && tail -F /dev/null"

