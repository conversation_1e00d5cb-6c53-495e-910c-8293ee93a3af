#!/bin/bash

# 从.env文件加载变量
if [ -f .env ]; then
  export $(grep -E "^DOCKER_SWARM_NAME=|^DOCKER_SWARM_FILE=" .env | xargs)
fi

# 设置默认值
DOCKER_SWARM_NAME=${DOCKER_SWARM_NAME:-fxerp-test}
DOCKER_COMPOSE_FILE=${DOCKER_COMPOSE_FILE:-deploy.test.yml}

# 检查部署文件是否存在
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
  echo "Error: Deployment file '$DOCKER_COMPOSE_FILE' not found!"
  exit 1
fi

echo "Deploying stack: $DOCKER_SWARM_NAME using file: $DOCKER_COMPOSE_FILE"
docker stack deploy -c $DOCKER_COMPOSE_FILE $DOCKER_SWARM_NAME