#!/bin/bash

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 从.env文件加载变量
if [ -f .env ]; then
  export $(grep -E "^DOCKER_SWARM_NAME=|^DOCKER_SWARM_FILE=|^DOCKER_IMAGE_NAME=|^DOCKER_REGISTRY=|^DOCKER_KEEP_IMAGES=" .env | xargs)
fi

# 设置默认值
DOCKER_SWARM_NAME=${DOCKER_SWARM_NAME:-fxerp-test}
DOCKER_COMPOSE_FILE=${DOCKER_COMPOSE_FILE:-deploy.test.yml}
DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME:-fxerp}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-}
DOCKER_KEEP_IMAGES=${DOCKER_KEEP_IMAGES:-3}

# 生成镜像标签（使用时间戳）
DOCKER_IMAGE_TAG=$(date +"%Y%m%d-%H%M%S")
FULL_IMAGE_NAME="${DOCKER_REGISTRY}${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_TAG}"

log_info "开始部署流程..."
log_info "镜像名称: $FULL_IMAGE_NAME"
log_info "Docker Swarm名称: $DOCKER_SWARM_NAME"
log_info "部署文件: $DOCKER_COMPOSE_FILE"

# 检查部署文件是否存在
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
  log_error "部署文件 '$DOCKER_COMPOSE_FILE' 不存在!"
  exit 1
fi

# 检查Dockerfile是否存在
if [ ! -f "Dockerfile" ]; then
  log_error "Dockerfile 不存在!"
  exit 1
fi