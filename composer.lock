{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "4589f38a194868deee2112562ae0610b", "packages": [{"name": "brick/math", "version": "0.12.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/brick/math/0.12.3/brick-math-0.12.3.zip", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.3"}, "time": "2025-02-28T13:11:00+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/carbonphp/carbon-doctrine-types/2.1.0/carbonphp-carbon-doctrine-types-2.1.0.zip", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "time": "2023-12-11T17:09:12+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/doctrine/deprecations/1.1.5/doctrine-deprecations-1.1.5.zip", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/inflector/2.0.10/doctrine-inflector-2.0.10.zip", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/instantiator/1.5.0/doctrine-instantiator-1.5.0.zip", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "time": "2022-12-30T00:15:36+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/lexer/2.1.1/doctrine-lexer-2.1.1.zip", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "time": "2024-02-05T11:35:39+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/egulias/email-validator/3.2.6/egulias-email-validator-3.2.6.zip", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.6"}, "time": "2023-06-01T07:04:22+00:00"}, {"name": "fig/http-message-util", "version": "1.1.5", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/fig/http-message-util/1.1.5/fig-http-message-util-1.1.5.zip", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "suggest": {"psr/http-message": "The package containing the PSR-7 interfaces"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Fig\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Utility classes and constants for use with PSR-7 (psr/http-message)", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-message-util/issues", "source": "https://github.com/php-fig/http-message-util/tree/1.1.5"}, "time": "2020-11-24T22:02:12+00:00"}, {"name": "friendsofhyperf/tinker", "version": "v3.1.48", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/friendsofhyperf/tinker/v3.1.48/friendsofhyperf-tinker-v3.1.48.zip", "reference": "fecfb11cc257f467d384d70681dec8fa5e838241", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "psy/psysh": "^0.10.0 || ^0.11.0 || ^0.12.0"}, "suggest": {"hyperf/view-engine": "Suggest for using `Hyperf\\ViewEngine\\HtmlString`."}, "type": "library", "extra": {"hyperf": {"config": "FriendsOfHyperf\\Tinker\\ConfigProvider"}, "branch-alias": {"dev-main": "3.1-dev"}}, "autoload": {"psr-4": {"FriendsOfHyperf\\Tinker\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/huangdijia"}], "description": "The Powerful REPL for Hyperf.", "keywords": ["Tinker", "hyperf", "php", "swoole", "v3.1"], "support": {"docs": "https://hyperf.fans", "issues": "https://github.com/friendsofhyperf/components/issues", "pull-request": "https://github.com/friendsofhyperf/components/pulls", "source": "https://github.com/friendsofhyperf/components"}, "time": "2024-11-28T08:49:14+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/graham-campbell/result-type/v1.1.3/graham-campbell-result-type-v1.1.3.zip", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "time": "2024-07-20T21:45:45+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/guzzle/7.9.3/guzzlehttp-guzzle-7.9.3.zip", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/guzzlehttp/promises/2.2.0/guzzlehttp-promises-2.2.0.zip", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/psr7/2.7.1/guzzlehttp-psr7-2.7.1.zip", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "time": "2025-03-27T12:30:47+00:00"}, {"name": "hyperf/async-queue", "version": "v3.1.51", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/async-queue/v3.1.51/hyperf-async-queue-v3.1.51.zip", "reference": "29f49ba266dd0a5763e2c4d8edfe5a448f944ff0", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dispatch a event.", "hyperf/logger": "Required to use QueueHandleListener.", "hyperf/process": "Auto register the consumer process for server."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\AsyncQueue\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\AsyncQueue\\": "src/"}}, "license": ["MIT"], "description": "A async queue component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["async-queue", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-01-14T06:55:15+00:00"}, {"name": "hyperf/cache", "version": "v3.1.43", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/cache/v3.1.43/hyperf-cache-v3.1.43.zip", "reference": "1e3cc54cee776c8d32cf40912dee5d366383bc33", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/di": "Use cache annotations.", "hyperf/event": "Use listener to delete annotation cache."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Cache\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Cache\\": "src/"}}, "license": ["MIT"], "description": "A cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["cache", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-10-09T10:22:39+00:00"}, {"name": "hyperf/carbon", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/carbon/v3.1.42/hyperf-carbon-v3.1.42.zip", "reference": "02cad4cac9fac53ef662c78fecc41a94229f3c13", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/framework": "~3.1.0", "nesbot/carbon": "^2.0", "php": ">=8.1"}, "suggest": {"ramsey/uuid": "Required to use \\Ramsey\\Uuid\\Uuid (^4.7).", "symfony/uid": "Required to use \\Symfony\\Component\\Uid\\Ulid (^7.0)."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Carbon\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Carbon\\": "src/"}}, "license": ["MIT"], "description": "A Carbon for Hyperf", "keywords": ["carbon", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/carbon/issues", "source": "https://github.com/hyperf/carbon/tree/v3.1.42"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/code-parser", "version": "v3.1.52", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/code-parser/v3.1.52/hyperf-code-parser-v3.1.52.zip", "reference": "340fb9902465bfc6d26d8b5a6d8d369590ea6e57", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1"}, "suggest": {"jean85/pretty-package-versions": "Required to use PrettyVersions. (^1.2|^2.0)", "nikic/php-parser": "Required to use PhpParser. (^4.0)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\CodeParser\\": "src/"}}, "license": ["MIT"], "description": "A code parser component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["code-parser", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-02-27T07:40:13+00:00"}, {"name": "hyperf/codec", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/codec/v3.1.42/hyperf-codec-v3.1.42.zip", "reference": "effc71c25e2d53c00fcf41da8bca083ac8a0db0e", "shasum": ""}, "require": {"ext-json": "*", "ext-xml": "*", "hyperf/contract": "~3.1.0", "php": ">=8.1"}, "suggest": {"ext-igbinary": "Required to use IgbinarySerializerPacker."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Codec\\": "src/"}}, "license": ["MIT"], "description": "A codec component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["codec", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/collection", "version": "v3.1.52", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/collection/v3.1.52/hyperf-collection-v3.1.52.zip", "reference": "a56bfce5002c7d69838f7e82184a2e135e98e80a", "shasum": ""}, "require": {"hyperf/conditionable": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Collection\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Collection package which come from illuminate/collections", "homepage": "https://hyperf.io", "keywords": ["collection", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-02-17T03:58:59+00:00"}, {"name": "hyperf/command", "version": "v3.1.51", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/command/v3.1.51/hyperf-command-v3.1.51.zip", "reference": "e71af684e6f01140221b608b3d4f4cf6f78144fe", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1", "psr/event-dispatcher": "^1.0", "symfony/console": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to use listeners."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Command\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Command\\": "src/"}}, "license": ["MIT"], "description": "Command for hyperf", "keywords": ["command", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/command/issues", "source": "https://github.com/hyperf/command/tree/v3.1.51"}, "time": "2025-02-06T03:40:37+00:00"}, {"name": "hyperf/conditionable", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/conditionable/v3.1.42/hyperf-conditionable-v3.1.42.zip", "reference": "dec9dec9dbde14e20f3d7ba000c3302381019de1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Conditionable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/conditionable", "homepage": "https://hyperf.io", "keywords": ["conditionable", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/config", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/config/v3.1.42/hyperf-config-v3.1.42.zip", "reference": "1df5e310aab752d6195f89f5cc98daf3cdc4bb6e", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "symfony/finder": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/context": "Required to use config()", "hyperf/di": "Allows using @Value annotation", "hyperf/event": "Allows using @Value annotation", "hyperf/framework": "Allows using @Value annotation", "vlucas/phpdotenv": "Allows using enviroment value to override the config"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Config\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["./src/Functions.php"], "psr-4": {"Hyperf\\Config\\": "src/"}}, "license": ["MIT"], "description": "An independent component that provides configuration container.", "homepage": "https://hyperf.io", "keywords": ["config", "configuration", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/constants", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/constants/v3.1.42/hyperf-constants-v3.1.42.zip", "reference": "e1e1184779cd163f9603ce234e1ecccb6fe382ae", "shasum": ""}, "require": {"hyperf/di": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/translation": "Required to use translation."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Constants\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Constants\\": "src/"}}, "license": ["MIT"], "description": "A constants component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["constants", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/context", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/context/v3.1.42/hyperf-context-v3.1.42.zip", "reference": "ac666862d644db7d813342c880826a1fda599bdf", "shasum": ""}, "require": {"hyperf/engine": "^2.0", "php": ">=8.1"}, "suggest": {"swow/psr7-plus": "Required to use RequestContext and ResponseContext"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Context\\": "src/"}}, "license": ["MIT"], "description": "A coroutine/application context library.", "homepage": "https://hyperf.io", "keywords": ["Context", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/contract", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/contract/v3.1.42/hyperf-contract-v3.1.42.zip", "reference": "6ef2c7f98917c52ccda3a37ae65b190848dde6c4", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Contract\\": "src/"}}, "license": ["MIT"], "description": "The contracts of Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/coordinator", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/coordinator/v3.1.42/hyperf-coordinator-v3.1.42.zip", "reference": "a0497d2a260f166ab53fed2eca6bb4e48b49ef56", "shasum": ""}, "require": {"hyperf/engine": "^2.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Coordinator\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Coordinator", "homepage": "https://hyperf.io", "keywords": ["Coordinator", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/coroutine", "version": "v3.1.54", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/coroutine/v3.1.54/hyperf-coroutine-v3.1.54.zip", "reference": "5b474c4bb46be015f1340939d92931b96a0b0cad", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/engine": "^2.14.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Coroutine\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Coroutine", "homepage": "https://hyperf.io", "keywords": ["coroutine", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-04-14T01:38:29+00:00"}, {"name": "hyperf/crontab", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/crontab/v3.1.42/hyperf-crontab-v3.1.42.zip", "reference": "be1187515aabbfe96b2f6a5330b4ddd742e971c7", "shasum": ""}, "require": {"hyperf/conditionable": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.0", "php": ">=8.1"}, "suggest": {"hyperf/command": "Required to use command trigger.", "hyperf/process": "Auto register the Crontab process for server."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Crontab\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Crontab\\": "src/"}}, "license": ["MIT"], "description": "A crontab component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["crontab", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/database", "version": "v3.1.54", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/database/v3.1.54/hyperf-database-v3.1.54.zip", "reference": "efae6d4694652fe3bb61e2f39c7f0a2e6813762d", "shasum": ""}, "require": {"hyperf/code-parser": "~3.1.0", "hyperf/collection": "~3.1.23", "hyperf/conditionable": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"doctrine/dbal": "Required to rename columns (^3.0).", "hyperf/paginator": "Required to paginate the result set (~3.1.0).", "nikic/php-parser": "Required to use ModelCommand. (^4.0)", "php-di/phpdoc-reader": "Required to use ModelCommand. (^2.2)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Database\\": "src/"}}, "license": ["MIT"], "description": "A flexible database library.", "homepage": "https://hyperf.io", "keywords": ["database", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-04-14T01:39:13+00:00"}, {"name": "hyperf/database-pgsql", "version": "v3.1.48", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/database-pgsql/v3.1.48/hyperf-database-pgsql-v3.1.48.zip", "reference": "7bb3ae4ae8bb0b57920bdbd2bc5bbbb03199db2a", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/database": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Database\\PgSQL\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Database\\PgSQL\\": "src/"}}, "license": ["MIT"], "description": "A pgsql handler for hyperf/database.", "homepage": "https://hyperf.io", "keywords": ["database", "hyperf", "pgsql", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-12-11T08:00:38+00:00"}, {"name": "hyperf/db-connection", "version": "v3.1.44", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/db-connection/v3.1.44/hyperf-db-connection-v3.1.44.zip", "reference": "95dbb713fda5556106b803d0201e1631645985b5", "shasum": ""}, "require": {"hyperf/database": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/model-listener": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\DbConnection\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\DbConnection\\": "src/"}}, "license": ["MIT"], "description": "A hyperf db connection handler for hyperf/database.", "homepage": "https://hyperf.io", "keywords": ["Connection", "database", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-10-11T08:58:16+00:00"}, {"name": "hyperf/di", "version": "v3.1.53", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/di/v3.1.53/hyperf-di-v3.1.53.zip", "reference": "13a89409da739102c7fc308ef53bdd2694cb220b", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0", "hyperf/code-parser": "~3.1.0", "hyperf/pipeline": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "nikic/php-parser": "^4.1", "php": ">=8.1", "php-di/phpdoc-reader": "^2.2", "psr/container": "^1.0 || ^2.0", "symfony/finder": "^5.0 || ^6.0 || ^7.0", "vlucas/phpdotenv": "^5.0"}, "suggest": {"ext-pcntl": "Required to scan annotations.", "hyperf/config": "Require this component for annotation scan progress to retrieve the scan path."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Di\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Di\\": "src/"}}, "license": ["MIT"], "description": "A DI for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["annotation", "di", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-04-03T08:36:04+00:00"}, {"name": "hyperf/dispatcher", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/dispatcher/v3.1.42/hyperf-dispatcher-v3.1.42.zip", "reference": "5cbdfd586bb8c3bbbabed5a23cec7faf52b744b0", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-middleware": "^1.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Dispatcher\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Dispatcher\\": "src/"}}, "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dispatcher", "filter", "hyperf", "middleware", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/engine", "version": "v2.14.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/engine/v2.14.0/hyperf-engine-v2.14.0.zip", "reference": "74ec5130fa0838e285a24292a232ce455edccff8", "shasum": ""}, "require": {"hyperf/engine-contract": "~1.13.0", "php": ">=8.0"}, "conflict": {"ext-swoole": "<5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/guzzle": "^3.0", "hyperf/http-message": "^3.0", "mockery/mockery": "^1.5", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.4", "swoole/ide-helper": "5.*"}, "suggest": {"ext-sockets": "*", "ext-swoole": ">=5.0", "hyperf/http-message": "Required to use ResponseEmitter.", "psr/http-message": "Required to use WebSocket Frame."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Engine\\ConfigProvider"}, "branch-alias": {"dev-master": "2.14-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Engine\\": "src/"}}, "license": ["MIT"], "description": "Coroutine engine provided by swoole.", "keywords": ["engine", "hyperf", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/engine/issues", "source": "https://github.com/hyperf/engine/tree/v2.14.0"}, "time": "2025-04-13T15:11:22+00:00"}, {"name": "hyperf/engine-contract", "version": "v1.13.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/engine-contract/v1.13.0/hyperf-engine-contract-v1.13.0.zip", "reference": "26a18ec0375147546bf9702b0fd737fdd2cec1d6", "shasum": ""}, "require": {"php": ">=8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": ">=7.0", "psr/http-message": "^1.0", "swoole/ide-helper": "^4.5"}, "suggest": {"psr/http-message": "Required to use WebSocket Frame."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Hyperf\\Engine\\Contract\\": "src/"}}, "license": ["MIT"], "description": "Contract for Coroutine Engine", "keywords": ["contract", "coroutine", "engine", "hyperf", "php"], "support": {"issues": "https://github.com/hyperf/engine-contract/issues", "source": "https://github.com/hyperf/engine-contract/tree/v1.13.0"}, "time": "2025-04-13T14:48:14+00:00"}, {"name": "hyperf/event", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/event/v3.1.42/hyperf-event-v3.1.42.zip", "reference": "2b5fbbc94674a1a5e1622896eb3f7ecc80aa38c4", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/stdlib": "~3.1.0", "php": ">=8.1", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotatioins."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Event\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Event\\": "src/"}}, "license": ["MIT"], "description": "an event manager that implements PSR-14.", "homepage": "https://hyperf.io", "keywords": ["event", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/exception-handler", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/exception-handler/v3.1.42/hyperf-exception-handler-v3.1.42.zip", "reference": "df2135fb0ffe0bb61032911038aea6488077cdef", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/dispatcher": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"hyperf/di": "Required to use #[ExceptionHandler]", "hyperf/event": "Required to use listeners", "hyperf/framework": "Required to use listeners", "hyperf/stringable": "Required to use WhoopsExceptionHandler"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ExceptionHandler\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\ExceptionHandler\\": "src/"}}, "license": ["MIT"], "description": "Exception handler for hyperf", "homepage": "https://hyperf.io", "keywords": ["exception-handler", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/filesystem", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/filesystem/v3.1.42/hyperf-filesystem-v3.1.42.zip", "reference": "dd3ecec490554620c0298ac9bea2d81964686b01", "shasum": ""}, "require": {"hyperf/di": "~3.1.0", "league/flysystem": "^1.0 || ^2.0 || ^3.0", "php": ">=8.1"}, "suggest": {"hyperf/flysystem-oss": "Required to use aliyun oss adapter when use `league/flysystem` v2.0", "hyperf/guzzle": "required to use s3 adapter", "league/flysystem-aws-s3-v3": "required to use s3 adapter", "league/flysystem-memory": "required to use memory adapter", "overtrue/flysystem-cos": "Required to use cos adapter (^3.0|^4.0)", "overtrue/flysystem-qiniu": "Required to use qiniu adapter (^1.0|^2.0)", "xxtime/flysystem-aliyun-oss": "Required to use aliyun oss adapter when use `league/flysystem` v1.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Filesystem\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Filesystem\\": "src/"}}, "license": ["MIT"], "description": "flysystem integration for hyperf", "keywords": ["hyperf", "php"], "support": {"issues": "https://github.com/hyperf/filesystem/issues", "source": "https://github.com/hyperf/filesystem/tree/v3.1.42"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/framework", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/framework/v3.1.42/hyperf-framework-v3.1.42.zip", "reference": "7b317d3891698a1eb0308e7306730d2ada1d6ff4", "shasum": ""}, "require": {"fig/http-message-util": "^1.1.2", "hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/coroutine": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"ext-swoole": "Required to use swoole engine.", "hyperf/command": "Required to use Command annotation.", "hyperf/di": "Required to use Command annotation.", "hyperf/dispatcher": "Required to use BootApplication event.", "symfony/event-dispatcher": "Required to use symfony event dispatcher (^5.0|^6.0)."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Framework\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Framework\\": "src/"}}, "license": ["MIT"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "homepage": "https://hyperf.io", "keywords": ["Microservice", "framework", "hyperf", "middleware", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/guzzle", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/guzzle/v3.1.42/hyperf-guzzle-v3.1.42.zip", "reference": "fe838557530bf7b2d39dc604563c3a3ff8d5618f", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "hyperf/pool": "Required to use pool handler."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Guzzle\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Guzzle\\": "src/"}}, "license": ["MIT"], "description": "Swoole coroutine handler for guzzle", "keywords": ["Guzzle", "handler", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/guzzle/issues", "source": "https://github.com/hyperf/guzzle/tree/v3.1.42"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/helper", "version": "v3.1.47", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/helper/v3.1.47/hyperf-helper-v3.1.47.zip", "reference": "2758b7cfab111bcf398b9de26e9e992ad3c18139", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/config": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/polyfill-coroutine": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/translation": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"]}, "license": ["MIT"], "description": "A function helper package that could help developer solved the problem quickly.", "homepage": "https://hyperf.io", "keywords": ["helper", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-11-28T01:51:55+00:00"}, {"name": "hyperf/http-message", "version": "v3.1.48", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/http-message/v3.1.48/hyperf-http-message-v3.1.48.zip", "reference": "534ce81af0feaa0c4a9e132af1c6a9c5527a8d85", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/engine": "^2.11", "hyperf/support": "~3.1.0", "laminas/laminas-mime": "^2.7", "php": ">=8.1", "psr/http-message": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"psr/container": "Required to replace RequestParserInterface."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\HttpMessage\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\HttpMessage\\": "src/"}}, "license": ["MIT"], "description": "microservice framework base on swoole", "keywords": ["http-message", "hyperf", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/http-message/issues", "source": "https://github.com/hyperf/http-message/tree/v3.1.48"}, "time": "2024-12-05T02:41:08+00:00"}, {"name": "hyperf/http-server", "version": "v3.1.55", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/http-server/v3.1.55/hyperf-http-server-v3.1.55.zip", "reference": "96a1d80cd9482942899d7068af0e6ecb78d382ce", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/dispatcher": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/exception-handler": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/serializer": "~3.1.0", "hyperf/server": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "nikic/fast-route": "^1.3", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\HttpServer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\HttpServer\\": "src/"}}, "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["http", "http-server", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-05-14T07:44:25+00:00"}, {"name": "hyperf/logger", "version": "v3.1.55", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/logger/v3.1.55/hyperf-logger-v3.1.55.zip", "reference": "8a80e2fab1521a08e2b8061a5dd65f5105f88ed6", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "monolog/monolog": "^2.7 || ^3.1", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Logger\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Logger\\": "src/"}}, "license": ["MIT"], "description": "A logger component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "logger", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-05-14T08:25:19+00:00"}, {"name": "hyperf/macroable", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/macroable/v3.1.42/hyperf-macroable-v3.1.42.zip", "reference": "0be650165b9e8ea073e199fac788ece70f16b6a4", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Macroable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/macroable", "homepage": "https://hyperf.io", "keywords": ["hyperf", "macroable", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/model-cache", "version": "v3.1.51", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/model-cache/v3.1.51/hyperf-model-cache-v3.1.51.zip", "reference": "70d1c97364cccef852c505a7eb1fcbfd8a62c1ec", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/db-connection": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/event": "Required to use DeleteCacheListener."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ModelCache\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\ModelCache\\": "src/"}}, "license": ["MIT"], "description": "A model cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-cache", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-02-06T07:02:37+00:00"}, {"name": "hyperf/model-listener", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/model-listener/v3.1.42/hyperf-model-listener-v3.1.42.zip", "reference": "0181882fb6034cf2eac81b84b5c65c187af9f3a4", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/database": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ModelListener\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\ModelListener\\": "src/"}}, "license": ["MIT"], "description": "A model listener for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-listener", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/paginator", "version": "v3.1.49", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/paginator/v3.1.49/hyperf-paginator-v3.1.49.zip", "reference": "dd9d59f5601fbdaa69f488348c7debd1931feb7b", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/event": "Reqiured to use PageResolverListener.", "hyperf/framework": "Reqiured to use PageResolverListener.", "hyperf/http-server": "Reqiured to use PageResolverListener."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Paginator\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Paginator\\": "src/"}}, "license": ["MIT"], "description": "A paginator component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "paginator", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-12-17T09:42:13+00:00"}, {"name": "hyperf/pipeline", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/pipeline/v3.1.42/hyperf-pipeline-v3.1.42.zip", "reference": "096d9a9f87ddea33209f134d30ae8d8867a195c7", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Pipeline\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/pipeline", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "pipeline", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/polyfill-coroutine", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/polyfill-coroutine/v3.1.42/hyperf-polyfill-coroutine-v3.1.42.zip", "reference": "bcc68934e70b01c6b91708420d2117fcd7de8df5", "shasum": ""}, "require": {"hyperf/coroutine": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"]}, "license": ["MIT"], "description": "This component provides features added short functions.", "homepage": "https://hyperf.io", "keywords": ["coroutine", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/pool", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/pool/v3.1.42/hyperf-pool-v3.1.42.zip", "reference": "004dd811bf760ea0032913a31284102742abb737", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "suggest": {"psr/event-dispatcher": "Required to use events."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Pool\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Pool\\": "src/"}}, "license": ["MIT"], "description": "An independent universal connection pool component.", "homepage": "https://hyperf.io", "keywords": ["connection-pool", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/process", "version": "v3.1.48", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/process/v3.1.48/hyperf-process-v3.1.48.zip", "reference": "8d68398bdb4f2623af1bec846399b6ce29bd7d2c", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dump the message before and after process.", "hyperf/framework": "Required to use BootProcessListener."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Process\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Process\\": "src/"}}, "license": ["MIT"], "description": "A process component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "process"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-12-02T10:54:30+00:00"}, {"name": "hyperf/redis", "version": "v3.1.53", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/redis/v3.1.53/hyperf-redis-v3.1.53.zip", "reference": "d481e97926d8e12b41bfeb1ebf5d88e66482da64", "shasum": ""}, "require": {"ext-redis": "^5.0 || ^6.0", "hyperf/contract": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "suggest": {"ext-redis": "Required to use sentinel mode (>=5.2).", "hyperf/di": "Create the RedisPool via dependency injection."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Redis\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Redis\\": "src/"}}, "license": ["MIT"], "description": "A redis component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "pool", "redis"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-04-03T07:31:20+00:00"}, {"name": "hyperf/serializer", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/serializer/v3.1.42/hyperf-serializer-v3.1.42.zip", "reference": "03c8a4840e0a7be83670c2fb0f850a2204fad076", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/di": "Required to use ExceptionNormalizer", "symfony/property-access": "Required to use SymfonyNormalizer (^5.0|^6.0)", "symfony/serializer": "Required to use SymfonyNormalizer (^5.0|^6.0)"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Serializer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Serializer\\": "src/"}}, "license": ["MIT"], "description": "A serializer component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "tappable"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/server", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/server/v3.1.42/hyperf-server-v3.1.42.zip", "reference": "e10c5ce6d9b72d3ca9ad16d36977e2e64d975460", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/engine": "^2.8", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/console": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/event": "Dump the info after server start.", "hyperf/framework": "Dump the info after server start."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Server\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Server\\": "src/"}}, "license": ["MIT"], "description": "A base server library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "server", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/stdlib", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/stdlib/v3.1.42/hyperf-stdlib-v3.1.42.zip", "reference": "13393734a4cc6c9878390b1f6b0fc7e5202c6b59", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Stdlib\\": "src/"}}, "license": ["MIT"], "description": "A stdlib component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "stdlib", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/stringable", "version": "v3.1.50", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/stringable/v3.1.50/hyperf-stringable-v3.1.50.zip", "reference": "89ab60e9ccabf024f5afc81a72f630cc67ae6687", "shasum": ""}, "require": {"ext-mbstring": "*", "hyperf/collection": "~3.1.0", "hyperf/conditionable": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1"}, "suggest": {"doctrine/inflector": "Required to use plural and singular methods.(^2.0|^3.0)", "ramsey/uuid": "Required to use uuid and orderedUuid methods.(^4.7|^5.0)", "symfony/uid": "Required to use ulid method.(^5.0|^6.0)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Stringable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Stringable package which come from illuminate/support", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "stringable", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-01-08T06:57:58+00:00"}, {"name": "hyperf/support", "version": "v3.1.51", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/support/v3.1.51/hyperf-support-v3.1.51.zip", "reference": "8d630b496945f3ac791a570fe6c1bf481c3f28ed", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "php": ">=8.1"}, "suggest": {"nesbot/carbon": "Use Carbon as DateTime object.(^2.0)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Support\\": "src/"}}, "license": ["MIT"], "description": "A support component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "support", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-02-06T07:02:37+00:00"}, {"name": "hyperf/swagger", "version": "v3.1.45", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/swagger/v3.1.45/hyperf-swagger-v3.1.45.zip", "reference": "10ac9f1b74058600735b2ff10a8830fa89db96e5", "shasum": ""}, "require": {"hyperf/command": "~3.1.0", "php": ">=8.1", "zircote/swagger-php": "^4.6"}, "suggest": {"hyperf/validation": "Required to use SwaggerRequest."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Swagger\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Swagger\\": "src/"}}, "license": ["MIT"], "description": "A swagger library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swagger", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-11-11T13:13:19+00:00"}, {"name": "hyperf/tappable", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/tappable/v3.1.42/hyperf-tappable-v3.1.42.zip", "reference": "f5c5d343c95238dcb3fe500876ceadc175e221f8", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Tappable\\": "src/"}}, "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/tappable", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "tappable"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/translation", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/translation/v3.1.42/hyperf-translation-v3.1.42.zip", "reference": "0bca5490a99b0ea5200d5753fd096338ec24c25f", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Translation\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Translation\\": "src/"}}, "license": ["MIT"], "description": "An independent translation component, forked by illuminate/translation.", "keywords": ["hyperf", "translation"], "support": {"issues": "https://github.com/hyperf/translation/issues", "source": "https://github.com/hyperf/translation/tree/v3.1.42"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/utils", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/utils/v3.1.42/hyperf-utils-v3.1.42.zip", "reference": "4b13a567a61d08a3c4d058499e28a5b26fc18f1c", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "hyperf/code-parser": "~3.1.0", "hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/engine": "^2.0", "hyperf/macroable": "~3.1.0", "hyperf/serializer": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "license": ["MIT"], "description": "A tools package that could help developer solved the problem quickly.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "utils"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/validation", "version": "v3.1.53", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/validation/v3.1.53/hyperf-validation-v3.1.53.zip", "reference": "476ccad33326073cd07ca66b7c6d5c1f4f3c7f53", "shasum": ""}, "require": {"egulias/email-validator": "^3.0", "hyperf/collection": "~3.1.0", "hyperf/conditionable": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/translation": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.21", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "suggest": {"hyperf/database": "Required if you want to use the database validation rule (~3.1.0).", "hyperf/http-server": "Required if you want to use the request validation rule (~3.1.0)."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Validation\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Validation\\": "src/"}}, "license": ["MIT"], "description": "hyperf validation", "keywords": ["hyperf", "validation"], "support": {"issues": "https://github.com/hyperf/validation/issues", "source": "https://github.com/hyperf/validation/tree/v3.1.53"}, "time": "2025-04-03T07:47:47+00:00"}, {"name": "hyperf/view", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/view/v3.1.42/hyperf-view-v3.1.42.zip", "reference": "bc8b1eb4378b7f61a9d6f478d6f0b84dc4734dc0", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"duncan3dc/blade": "Required to use blade as a view render engine.", "hyperf/task": "Required to use task as a view render mode.", "league/plates": "Required to use plates as a view render engine.", "smarty/smarty": "Required to use smarty as a view render engine.", "twig/twig": "Required to use twig as a view render engine."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\View\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\View\\": "src/"}}, "license": ["MIT"], "description": "A view library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "view"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/view-engine", "version": "v3.1.52", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/hyperf/view-engine/v3.1.52/hyperf-view-engine-v3.1.52.zip", "reference": "59124d7bcf580ee22159d7ed4916f52f2a2e803c", "shasum": ""}, "require": {"ext-json": "*", "hyperf/collection": "~3.1.23", "hyperf/config": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "hyperf/view": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/session": "Required to use ShareErrorsFromSession.", "hyperf/validation": "Required to use ShareErrorsFromSession and ValidationExceptionHandle."}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ViewEngine\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\ViewEngine\\": "src/"}}, "license": ["MIT"], "keywords": ["engine", "hyperf", "php", "view"], "support": {"issues": "https://github.com/hyperf/view-engine/issues", "source": "https://github.com/hyperf/view-engine/tree/v3.1.52"}, "time": "2025-02-27T06:49:30+00:00"}, {"name": "laminas/laminas-mime", "version": "2.12.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/laminas/laminas-mime/2.12.0/laminas-laminas-mime-2.12.0.zip", "reference": "08cc544778829b7d68d27a097885bd6e7130135e", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^2.7 || ^3.0", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-mime": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "laminas/laminas-mail": "^2.19.0", "phpunit/phpunit": "~9.5.25"}, "suggest": {"laminas/laminas-mail": "Laminas\\Mail component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Mime\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "Create and parse MIME messages and parts", "homepage": "https://laminas.dev", "keywords": ["laminas", "mime"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mime/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mime/issues", "rss": "https://github.com/laminas/laminas-mime/releases.atom", "source": "https://github.com/laminas/laminas-mime"}, "time": "2023-11-02T16:47:19+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.20.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/laminas/laminas-stdlib/3.20.0/laminas-laminas-stdlib-3.20.0.zip", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "time": "2024-10-29T13:46:07+00:00"}, {"name": "lcobucci/jwt", "version": "5.3.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/lcobucci/jwt/5.3.0/lcobucci-jwt-5.3.0.zip", "reference": "08071d8d2c7f4b00222cc4b1fb6aa46990a80f83", "shasum": ""}, "require": {"ext-openssl": "*", "ext-sodium": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "psr/clock": "^1.0"}, "require-dev": {"infection/infection": "^0.27.0", "lcobucci/clock": "^3.0", "lcobucci/coding-standard": "^11.0", "phpbench/phpbench": "^1.2.9", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.10.7", "phpstan/phpstan-deprecation-rules": "^1.1.3", "phpstan/phpstan-phpunit": "^1.3.10", "phpstan/phpstan-strict-rules": "^1.5.0", "phpunit/phpunit": "^10.2.6"}, "suggest": {"lcobucci/clock": ">= 3.0"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/5.3.0"}, "time": "2024-04-11T23:07:54+00:00"}, {"name": "league/flysystem", "version": "3.29.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/league/flysystem/3.29.1/league-flysystem-3.29.1.zip", "reference": "edc1bb7c86fab0776c3287dbd19b5fa278347319", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-mongodb": "^1.3", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "guzzlehttp/psr7": "^2.6", "microsoft/azure-storage-blob": "^1.1", "mongodb/mongodb": "^1.2", "phpseclib/phpseclib": "^3.0.36", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.6.0"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.29.1"}, "time": "2024-10-08T08:58:34+00:00"}, {"name": "league/flysystem-local", "version": "3.29.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/league/flysystem-local/3.29.0/league-flysystem-local-3.29.0.zip", "reference": "e0e8d52ce4b2ed154148453d321e97c8e931bd27", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.29.0"}, "time": "2024-08-09T21:24:39+00:00"}, {"name": "league/mime-type-detection", "version": "1.16.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/league/mime-type-detection/1.16.0/league-mime-type-detection-1.16.0.zip", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.16.0"}, "time": "2024-09-21T08:32:55+00:00"}, {"name": "mineadmin/access", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/access/v3.0.1/mineadmin-access-v3.0.1.zip", "reference": "4c57fc4032c8e1697cc56284b0670c334ce0f3f3", "shasum": ""}, "require": {"hyperf/di": "~3.1"}, "type": "library", "extra": {"hyperf": {"config": "Mine\\Access\\ConfigProvider"}}, "autoload": {"psr-4": {"Mine\\Access\\": "./"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "Mineadmin Access Component", "support": {"issues": "https://github.com/mineadmin/Access/issues", "source": "https://github.com/mineadmin/Access/tree/v3.0.1"}, "time": "2024-10-07T14:06:26+00:00"}, {"name": "mineadmin/app-store", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/app-store/v3.0.1/mineadmin-app-store-v3.0.1.zip", "reference": "560324020fff793df84e8583b8d4030a955dac0f", "shasum": ""}, "require": {"ext-zip": "*", "hyperf/guzzle": "~3.1", "hyperf/translation": "~3.1.0", "nette/utils": "dev-master"}, "type": "library", "extra": {"hyperf": {"config": "Mine\\AppStore\\ConfigProvider"}}, "autoload": {"psr-4": {"Mine\\AppStore\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "Mineadmin 2.0 AppStore Extension", "support": {"issues": "https://github.com/mineadmin/AppStore/issues", "source": "https://github.com/mineadmin/AppStore/tree/v3.0.1"}, "time": "2024-12-17T06:07:19+00:00"}, {"name": "mineadmin/auth-jwt", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/auth-jwt/v3.0.1/mineadmin-auth-jwt-v3.0.1.zip", "reference": "0ec066c212e0059fc836fa20b0dd4a2d359df895", "shasum": ""}, "type": "library", "extra": {"hyperf": {"config": "Mine\\JwtAuth\\ConfigProvider"}}, "autoload": {"psr-4": {"Mine\\JwtAuth\\": "./"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "Mineadmin New Jwt Auth Component", "support": {"issues": "https://github.com/mineadmin/JwtAuth/issues", "source": "https://github.com/mineadmin/JwtAuth/tree/v3.0.1"}, "time": "2024-12-21T12:51:12+00:00"}, {"name": "mineadmin/core", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/core/v3.0.1/mineadmin-core-v3.0.1.zip", "reference": "9a99b8f7fa7d92d14b67c82db2bd15873357c99e", "shasum": ""}, "require": {"hyperf/async-queue": "~3.1", "hyperf/framework": "~3.1"}, "type": "library", "autoload": {"psr-4": {"Mine\\Core\\": "./"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "Mineadmin Core Component", "support": {"issues": "https://github.com/mineadmin/Core/issues", "source": "https://github.com/mineadmin/Core/tree/v3.0.1"}, "time": "2024-11-27T08:49:28+00:00"}, {"name": "mineadmin/crontab", "version": "v2.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/mineadmin/crontab/v2.0.2/mineadmin-crontab-v2.0.2.zip", "reference": "9b85b7ea5f7146075a33df2938923b04ff4a39d0", "shasum": ""}, "require": {"hyperf/crontab": "^3.1", "hyperf/database": "^3.1", "hyperf/db-connection": "^3.1", "php": ">=8.1"}, "type": "library", "extra": {"hyperf": {"config": "Mine\\Crontab\\ConfigProvider"}}, "autoload": {"psr-4": {"Mine\\Crontab\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "x.mo", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "加强 Hyperf Crontab", "support": {"issues": "https://github.com/mineadmin/Crontab/issues", "source": "https://github.com/mineadmin/Crontab/tree/v2.0.2"}, "time": "2025-03-05T02:42:31+00:00"}, {"name": "mineadmin/jwt", "version": "v3.0.2", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/jwt/v3.0.2/mineadmin-jwt-v3.0.2.zip", "reference": "fbb19b12bb814e2e5b7151368bf4795fa40c7d25", "shasum": ""}, "require": {"hyperf/cache": "~3.1", "hyperf/http-server": "~3.1", "lcobucci/jwt": "~5.3.0"}, "type": "library", "autoload": {"psr-4": {"Mine\\Jwt\\": "./"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "Mineadmin jwt component", "support": {"issues": "https://github.com/mineadmin/Jwt/issues", "source": "https://github.com/mineadmin/Jwt/tree/v3.0.2"}, "time": "2025-01-10T15:05:52+00:00"}, {"name": "mineadmin/support", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/support/v3.0.1/mineadmin-support-v3.0.1.zip", "reference": "bbedc6296181746dc97648cda8cee24d19d90abf", "shasum": ""}, "require": {"ext-fileinfo": "*", "hyperf/logger": "~3.1"}, "type": "library", "autoload": {"psr-4": {"Mine\\Support\\": "./"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "email": "<EMAIL>", "role": "Developer"}], "description": "MineAdmin Support component", "support": {"issues": "https://github.com/mineadmin/support/issues", "source": "https://github.com/mineadmin/support/tree/v3.0.1"}, "time": "2024-12-10T03:56:50+00:00"}, {"name": "mineadmin/swagger", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/swagger/v3.0.1/mineadmin-swagger-v3.0.1.zip", "reference": "694de2c32c609fcdd6b87aa9feb05461048362c2", "shasum": ""}, "require": {"hyperf/di": "~3.1", "hyperf/swagger": "~3.1"}, "type": "library", "extra": {"hyperf": {"config": "Mine\\Swagger\\ConfigProvider"}}, "autoload": {"psr-4": {"Mine\\Swagger\\": "./"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "Mineadmin Swagger component", "support": {"issues": "https://github.com/mineadmin/Swagger/issues", "source": "https://github.com/mineadmin/Swagger/tree/v3.0.1"}, "time": "2024-10-07T14:06:26+00:00"}, {"name": "mineadmin/upload", "version": "v3.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/mineadmin/upload/v3.0.1/mineadmin-upload-v3.0.1.zip", "reference": "b90ec45b236f5a8db673aa7773d8bbf438abb29d", "shasum": ""}, "require": {"ext-fileinfo": "*", "hyperf/filesystem": "~3.1", "hyperf/logger": "~3.1", "ramsey/uuid": "^4.7"}, "type": "library", "autoload": {"psr-4": {"Mine\\Upload\\": "./"}}, "license": ["MIT"], "authors": [{"name": "xmo", "email": "<EMAIL>", "role": "Developer"}, {"name": "zds", "role": "Developer"}], "description": "Mineadmin Upload component", "support": {"issues": "https://github.com/mineadmin/Upload/issues", "source": "https://github.com/mineadmin/Upload/tree/v3.0.1"}, "time": "2025-01-03T03:15:13+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/monolog/monolog/3.9.0/monolog-monolog-3.9.0.zip", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "time": "2025-03-24T10:02:05+00:00"}, {"name": "nesbot/carbon", "version": "2.73.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/nesbot/carbon/2.73.0/nesbot-carbon-2.73.0.zip", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "time": "2025-01-08T20:10:23+00:00"}, {"name": "nette/utils", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/nette/utils/dev-master/nette-utils-dev-master.zip", "reference": "2d3e496c5cb2a7189e3f746c644fceb4f5f88add", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/master"}, "time": "2025-04-05T17:09:45+00:00"}, {"name": "nikic/fast-route", "version": "v1.3.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/nikic/fast-route/v1.3.0/nikic-fast-route-v1.3.0.zip", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "time": "2018-02-13T20:26:39+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/nikic/php-parser/v4.19.4/nikic-php-parser-v4.19.4.zip", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "php-di/phpdoc-reader", "version": "2.2.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/php-di/phpdoc-reader/2.2.1/php-di-phpdoc-reader-2.2.1.zip", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"mnapoli/hard-mode": "~0.3.0", "phpunit/phpunit": "^8.5|^9.0"}, "type": "library", "autoload": {"psr-4": {"PhpDocReader\\": "src/PhpDocReader"}}, "license": ["MIT"], "description": "PhpDocReader parses @var and @param values in PHP docblocks (supports namespaced class names with the same resolution rules as PHP)", "keywords": ["phpdoc", "reflection"], "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/2.2.1"}, "time": "2020-10-12T12:39:22+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpoption/phpoption/1.9.3/phpoption-phpoption-1.9.3.zip", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "time": "2024-07-20T21:41:07+00:00"}, {"name": "psr/clock", "version": "1.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/clock/1.0.0/psr-clock-1.0.0.zip", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/container/2.0.2/psr-container-2.0.2.zip", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/event-dispatcher/1.0.0/psr-event-dispatcher-1.0.0.zip", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/http-client/1.0.3/psr-http-client-1.0.3.zip", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-factory/1.1.0/psr-http-factory-1.1.0.zip", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-message/2.0/psr-http-message-2.0.zip", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-server-handler/1.0.2/psr-http-server-handler-1.0.2.zip", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-server-middleware/1.0.2/psr-http-server-middleware-1.0.2.zip", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/log", "version": "3.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/log/3.0.2/psr-log-3.0.2.zip", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/simple-cache/3.0.0/psr-simple-cache-3.0.0.zip", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "psy/psysh", "version": "v0.12.8", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psy/psysh/v0.12.8/psy-psysh-v0.12.8.zip", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.8"}, "time": "2025-03-16T03:05:19+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/ralouphie/getallheaders/3.0.3/ralouphie-getallheaders-3.0.3.zip", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.1.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/ramsey/collection/2.1.1/ramsey-collection-2.1.1.zip", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "time": "2025-03-22T05:38:12+00:00"}, {"name": "ramsey/uuid", "version": "4.7.6", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/ramsey/uuid/4.7.6/ramsey-uuid-4.7.6.zip", "reference": "91039bc1faa45ba123c4328958e620d382ec7088", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.6"}, "time": "2024-04-27T21:32:50+00:00"}, {"name": "swow/psr7-plus", "version": "v1.1.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/swow/psr7-plus/v1.1.2/swow-psr7-plus-v1.1.2.zip", "reference": "7acc4924be907d2ff64edee5a2bd116620e56364", "shasum": ""}, "require": {"php": ">=8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1|^2.0"}, "type": "library", "autoload": {"psr-4": {"Swow\\Psr7\\Message\\": "src/Message/"}}, "license": ["Apache-2.0"], "authors": [{"name": "twose", "email": "<EMAIL>"}], "description": "Modern strong-typed interfaces for Psr7, not only HTTP but also WebSocket", "keywords": ["http", "psr17", "psr7", "swow", "websocket"], "support": {"issues": "https://github.com/swow/swow", "source": "https://github.com/swow/psr7-plus/tree/v1.1.2"}, "time": "2023-06-15T09:18:11+00:00"}, {"name": "symfony/console", "version": "v6.4.21", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/console/v6.4.21/symfony-console-v6.4.21.zip", "reference": "a3011c7b7adb58d89f6c0d822abb641d7a5f9719", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.21"}, "time": "2025-04-07T15:42:41+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/deprecation-contracts/v3.5.1/symfony-deprecation-contracts-v3.5.1.zip", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/finder/v6.4.17/symfony-finder-v6.4.17.zip", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "time": "2024-12-29T13:51:37+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-ctype/v1.32.0/symfony-polyfill-ctype-v1.32.0.zip", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-intl-grapheme/v1.32.0/symfony-polyfill-intl-grapheme-v1.32.0.zip", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-intl-idn/v1.32.0/symfony-polyfill-intl-idn-v1.32.0.zip", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-intl-normalizer/v1.32.0/symfony-polyfill-intl-normalizer-v1.32.0.zip", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/polyfill-mbstring/v1.32.0/symfony-polyfill-mbstring-v1.32.0.zip", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-php80/v1.32.0/symfony-polyfill-php80-v1.32.0.zip", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/service-contracts/v3.5.1/symfony-service-contracts-v3.5.1.zip", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v6.4.21", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/string/v6.4.21/symfony-string-v6.4.21.zip", "reference": "73e2c6966a5aef1d4892873ed5322245295370c6", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.21"}, "time": "2025-04-18T15:23:29+00:00"}, {"name": "symfony/translation", "version": "v6.4.21", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/translation/v6.4.21/symfony-translation-v6.4.21.zip", "reference": "bb92ea5588396b319ba43283a5a3087a034cb29c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.21"}, "time": "2025-04-07T19:02:30+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/translation-contracts/v3.5.1/symfony-translation-contracts-v3.5.1.zip", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.21", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/var-dumper/v6.4.21/symfony-var-dumper-v6.4.21.zip", "reference": "22560f80c0c5cd58cc0bcaf73455ffd81eb380d5", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.21"}, "time": "2025-04-09T07:34:50+00:00"}, {"name": "symfony/yaml", "version": "v6.4.21", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/yaml/v6.4.21/symfony-yaml-v6.4.21.zip", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.21"}, "time": "2025-04-04T09:48:44+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/vlucas/phpdotenv/v5.6.2/vlucas-phpdotenv-v5.6.2.zip", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.2"}, "time": "2025-04-30T23:37:27+00:00"}, {"name": "zircote/swagger-php", "version": "4.10.6", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/zircote/swagger-php/4.10.6/zircote-swagger-php-4.10.6.zip", "reference": "e462ff5269ea0ec91070edd5d51dc7215bdea3b6", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.2", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2 || ^3", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.7 || ^2.0", "friendsofphp/php-cs-fixer": "^2.17 || ^3.47.1", "phpstan/phpstan": "^1.6", "phpunit/phpunit": ">=8", "vimeo/psalm": "^4.23"}, "suggest": {"doctrine/annotations": "^1.7 || ^2.0"}, "bin": ["bin/openapi"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"OpenApi\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bfanger.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://radebatz.net"}], "description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.10.6"}, "time": "2024-07-26T03:04:43+00:00"}], "packages-dev": [{"name": "clue/ndjson-react", "version": "v1.3.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/clue/ndjson-react/v1.3.0/clue-ndjson-react-v1.3.0.zip", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0", "shasum": ""}, "require": {"php": ">=5.3", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "react/event-loop": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Clue\\React\\NDJson\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Streaming newline-delimited JSON (NDJSON) parser and encoder for ReactPHP.", "homepage": "https://github.com/clue/reactphp-ndjson", "keywords": ["NDJSON", "json", "jsonlines", "newline", "reactphp", "streaming"], "support": {"issues": "https://github.com/clue/reactphp-ndjson/issues", "source": "https://github.com/clue/reactphp-ndjson/tree/v1.3.0"}, "time": "2022-12-23T10:58:28+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/composer/pcre/3.3.2/composer-pcre-3.3.2.zip", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/composer/semver/3.4.3/composer-semver-3.4.3.zip", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "time": "2024-09-19T14:15:21+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/composer/xdebug-handler/3.0.5/composer-xdebug-handler-3.0.5.zip", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "time": "2024-05-06T16:37:16+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/cache/2.2.0/doctrine-cache-2.2.0.zip", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "3.9.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/dbal/3.9.4/doctrine-dbal-3.9.4.zip", "reference": "ec16c82f20be1a7224e65ac67144a29199f87959", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "9.6.22", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.2", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.9.4"}, "time": "2025-01-16T08:28:55+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/event-manager/2.0.1/doctrine-event-manager-2.0.1.zip", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "time": "2024-05-22T20:47:39+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/evenement/evenement/v3.0.2/evenement-evenement-v3.0.2.zip", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "support": {"issues": "https://github.com/igorw/evenement/issues", "source": "https://github.com/igorw/evenement/tree/v3.0.2"}, "time": "2023-08-08T05:53:35+00:00"}, {"name": "fakerphp/faker", "version": "v1.24.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/fakerphp/faker/v1.24.1/fakerphp-faker-v1.24.1.zip", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"}, "time": "2024-11-21T13:46:39+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.2.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/fidry/cpu-core-counter/1.2.0/fidry-cpu-core-counter-1.2.0.zip", "reference": "8520451a140d3f46ac33042715115e290cf5785f", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.2.0"}, "time": "2024-08-06T10:04:20+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.75.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/friendsofphp/php-cs-fixer/v3.75.0/friendsofphp-php-cs-fixer-v3.75.0.zip", "reference": "399a128ff2fdaf4281e4e79b755693286cdf325c", "shasum": ""}, "require": {"clue/ndjson-react": "^1.0", "composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.3", "ext-filter": "*", "ext-hash": "*", "ext-json": "*", "ext-tokenizer": "*", "fidry/cpu-core-counter": "^1.2", "php": "^7.4 || ^8.0", "react/child-process": "^0.6.5", "react/event-loop": "^1.0", "react/promise": "^2.0 || ^3.0", "react/socket": "^1.0", "react/stream": "^1.0", "sebastian/diff": "^4.0 || ^5.1 || ^6.0 || ^7.0", "symfony/console": "^5.4 || ^6.4 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.4 || ^7.0", "symfony/filesystem": "^5.4 || ^6.4 || ^7.0", "symfony/finder": "^5.4 || ^6.4 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.4 || ^7.0", "symfony/polyfill-mbstring": "^1.31", "symfony/polyfill-php80": "^1.31", "symfony/polyfill-php81": "^1.31", "symfony/process": "^5.4 || ^6.4 || ^7.2", "symfony/stopwatch": "^5.4 || ^6.4 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3.1 || ^2.6", "infection/infection": "^0.29.14", "justinrainbow/json-schema": "^5.3 || ^6.2", "keradus/cli-executor": "^2.1", "mikey179/vfsstream": "^1.6.12", "php-coveralls/php-coveralls": "^2.7", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.6", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.6", "phpunit/phpunit": "^9.6.22 || ^10.5.45 || ^11.5.12", "symfony/var-dumper": "^5.4.48 || ^6.4.18 || ^7.2.3", "symfony/yaml": "^5.4.45 || ^6.4.18 || ^7.2.3"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "exclude-from-classmap": ["src/Fixer/Internal/*"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "keywords": ["Static code analysis", "fixer", "standards", "static analysis"], "support": {"issues": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/issues", "source": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/tree/v3.75.0"}, "time": "2025-03-31T18:40:42+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.1.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hamcrest/hamcrest-php/v2.1.1/hamcrest-hamcrest-php-v2.1.1.zip", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0 || ^3.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.1.1"}, "time": "2025-04-30T06:54:44+00:00"}, {"name": "hyperf/devtool", "version": "v3.1.51", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/devtool/v3.1.51/hyperf-devtool-v3.1.51.zip", "reference": "b032916fa51293f894046754f596f7d6d71352df", "shasum": ""}, "require": {"hyperf/code-parser": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Devtool\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Devtool\\": "src/"}}, "license": ["MIT"], "description": "A Devtool for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dev", "devtool", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2025-02-06T06:42:00+00:00"}, {"name": "hyperf/ide-helper", "version": "v3.1.42", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/ide-helper/v3.1.42/hyperf-ide-helper-v3.1.42.zip", "reference": "090487aaf7050f3887f30bcbceae2f27a3a3710e", "shasum": ""}, "require": {"hyperf/code-parser": "~3.1.0", "hyperf/support": "~3.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "license": ["MIT"], "description": "IDE help files for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dev", "hyperf", "ide-helper", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "time": "2024-09-25T02:54:12+00:00"}, {"name": "hyperf/testing", "version": "v3.1.55", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/testing/v3.1.55/hyperf-testing-v3.1.55.zip", "reference": "f7daebdcc7aa4520acf61689f7e2e4049e9247db", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/http-server": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "phpunit/phpunit": "^10.0", "psr/container": "^1.0 || ^2.0", "symfony/http-foundation": "^5.4 || ^6.0"}, "suggest": {"fakerphp/faker": "Required to use Faker feature.(^1.23)"}, "bin": ["co-php<PERSON>t"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Hyperf\\Testing\\": "src/"}}, "license": ["MIT"], "description": "Testing for hyperf", "keywords": ["dev", "php", "swoole", "testing"], "support": {"source": "https://github.com/hyperf/testing/tree/v3.1.55"}, "time": "2025-05-02T14:13:24+00:00"}, {"name": "hyperf/watcher", "version": "v3.1.54", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/hyperf/watcher/v3.1.54/hyperf-watcher-v3.1.54.zip", "reference": "c92dc6bd94c6e2369a3de262a700550427041b70", "shasum": ""}, "require": {"ext-posix": "*", "hyperf/codec": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1"}, "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Watcher\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Watcher\\": "src/"}}, "license": ["MIT"], "description": "Hot reload watcher for Hyperf", "keywords": ["dev", "hyperf", "php"], "support": {"issues": "https://github.com/hyperf/watcher/issues", "source": "https://github.com/hyperf/watcher/tree/v3.1.54"}, "time": "2025-04-26T13:02:01+00:00"}, {"name": "mockery/mockery", "version": "1.6.12", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/mockery/mockery/1.6.12/mockery-mockery-1.6.12.zip", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-05-16T03:13:13+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/myclabs/deep-copy/1.13.1/myclabs-deep-copy-1.13.1.zip", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "time": "2025-04-29T12:36:36+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phar-io/manifest/2.0.4/phar-io-manifest-2.0.4.zip", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phar-io/version/3.2.1/phar-io-version-3.2.1.zip", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpstan/phpstan", "version": "2.1.16", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpstan/phpstan/2.1.16/phpstan-phpstan-2.1.16.zip", "reference": "b8c1cf533cba0c305d91c6ccd23f3dd0566ba5f9", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "time": "2025-05-16T09:40:10+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.16", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-code-coverage/10.1.16/phpunit-php-code-coverage-10.1.16.zip", "reference": "7e308268858ed6baedc8704a304727d20bc07c77", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-text-template": "^3.0.1", "sebastian/code-unit-reverse-lookup": "^3.0.0", "sebastian/complexity": "^3.2.0", "sebastian/environment": "^6.1.0", "sebastian/lines-of-code": "^2.0.2", "sebastian/version": "^4.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.16"}, "time": "2024-08-22T04:31:57+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-file-iterator/4.1.0/phpunit-php-file-iterator-4.1.0.zip", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-invoker/4.0.0/phpunit-php-invoker-4.0.0.zip", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-text-template/3.0.1/phpunit-php-text-template-3.0.1.zip", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-timer/6.0.0/phpunit-php-timer-6.0.0.zip", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.46", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/phpunit/10.5.46/phpunit-phpunit-10.5.46.zip", "reference": "8080be387a5be380dda48c6f41cee4a13aadab3d", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.16", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-invoker": "^4.0.0", "phpunit/php-text-template": "^3.0.1", "phpunit/php-timer": "^6.0.0", "sebastian/cli-parser": "^2.0.1", "sebastian/code-unit": "^2.0.0", "sebastian/comparator": "^5.0.3", "sebastian/diff": "^5.1.1", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^5.1.2", "sebastian/global-state": "^6.0.2", "sebastian/object-enumerator": "^5.0.0", "sebastian/recursion-context": "^5.0.0", "sebastian/type": "^4.0.0", "sebastian/version": "^4.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.46"}, "time": "2025-05-02T06:46:24+00:00"}, {"name": "psr/cache", "version": "3.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/cache/3.0.0/psr-cache-3.0.0.zip", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "react/cache", "version": "v1.2.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/react/cache/v1.2.0/react-cache-v1.2.0.zip", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b", "shasum": ""}, "require": {"php": ">=5.3.0", "react/promise": "^3.0 || ^2.0 || ^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35"}, "type": "library", "autoload": {"psr-4": {"React\\Cache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, Promise-based cache interface for ReactPHP", "keywords": ["cache", "caching", "promise", "reactphp"], "support": {"issues": "https://github.com/reactphp/cache/issues", "source": "https://github.com/reactphp/cache/tree/v1.2.0"}, "time": "2022-11-30T15:59:55+00:00"}, {"name": "react/child-process", "version": "v0.6.6", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/react/child-process/v0.6.6/react-child-process-v0.6.6.zip", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/event-loop": "^1.2", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/socket": "^1.16", "sebastian/environment": "^5.0 || ^3.0 || ^2.0 || ^1.0"}, "type": "library", "autoload": {"psr-4": {"React\\ChildProcess\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven library for executing child processes with ReactPHP.", "keywords": ["event-driven", "process", "reactphp"], "support": {"issues": "https://github.com/reactphp/child-process/issues", "source": "https://github.com/reactphp/child-process/tree/v0.6.6"}, "time": "2025-01-01T16:37:48+00:00"}, {"name": "react/dns", "version": "v1.13.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/react/dns/v1.13.0/react-dns-v1.13.0.zip", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "shasum": ""}, "require": {"php": ">=5.3.0", "react/cache": "^1.0 || ^0.6 || ^0.5", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.7 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3 || ^2", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Dns\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async DNS resolver for ReactPHP", "keywords": ["async", "dns", "dns-resolver", "reactphp"], "support": {"issues": "https://github.com/reactphp/dns/issues", "source": "https://github.com/reactphp/dns/tree/v1.13.0"}, "time": "2024-06-13T14:18:03+00:00"}, {"name": "react/event-loop", "version": "v1.5.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/react/event-loop/v1.5.0/react-event-loop-v1.5.0.zip", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "suggest": {"ext-pcntl": "For signal handling support when using the StreamSelectLoop"}, "type": "library", "autoload": {"psr-4": {"React\\EventLoop\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["asynchronous", "event-loop"], "support": {"issues": "https://github.com/reactphp/event-loop/issues", "source": "https://github.com/reactphp/event-loop/tree/v1.5.0"}, "time": "2023-11-13T13:48:05+00:00"}, {"name": "react/promise", "version": "v3.2.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/react/promise/v3.2.0/react-promise-v3.2.0.zip", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.39 || 1.4.10", "phpunit/phpunit": "^9.6 || ^7.5"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v3.2.0"}, "time": "2024-05-24T10:39:05+00:00"}, {"name": "react/socket", "version": "v1.16.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/react/socket/v1.16.0/react-socket-v1.16.0.zip", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/dns": "^1.13", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.6 || ^1.2.1", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3.3 || ^2", "react/promise-stream": "^1.4", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Socket\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, streaming plaintext TCP/IP and secure TLS socket server and client connections for ReactPHP", "keywords": ["Connection", "Socket", "async", "reactphp", "stream"], "support": {"issues": "https://github.com/reactphp/socket/issues", "source": "https://github.com/reactphp/socket/tree/v1.16.0"}, "time": "2024-07-26T10:38:09+00:00"}, {"name": "react/stream", "version": "v1.4.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/react/stream/v1.4.0/react-stream-v1.4.0.zip", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.8", "react/event-loop": "^1.2"}, "require-dev": {"clue/stream-filter": "~1.2", "phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"React\\Stream\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven readable and writable streams for non-blocking I/O in ReactPHP", "keywords": ["event-driven", "io", "non-blocking", "pipe", "reactphp", "readable", "stream", "writable"], "support": {"issues": "https://github.com/reactphp/stream/issues", "source": "https://github.com/reactphp/stream/tree/v1.4.0"}, "time": "2024-06-11T12:45:25+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/cli-parser/2.0.1/sebastian-cli-parser-2.0.1.zip", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/code-unit/2.0.0/sebastian-code-unit-2.0.0.zip", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/code-unit-reverse-lookup/3.0.0/sebastian-code-unit-reverse-lookup-3.0.0.zip", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/comparator/5.0.3/sebastian-comparator-5.0.3.zip", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.3"}, "time": "2024-10-18T14:56:07+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/complexity/3.2.0/sebastian-complexity-3.2.0.zip", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/diff/5.1.1/sebastian-diff-5.1.1.zip", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/environment/6.1.0/sebastian-environment-6.1.0.zip", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/exporter/5.1.2/sebastian-exporter-5.1.2.zip", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/global-state/6.0.2/sebastian-global-state-6.0.2.zip", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/sebastian/lines-of-code/2.0.2/sebastian-lines-of-code-2.0.2.zip", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/object-enumerator/5.0.0/sebastian-object-enumerator-5.0.0.zip", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/object-reflector/3.0.0/sebastian-object-reflector-3.0.0.zip", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/recursion-context/5.0.0/sebastian-recursion-context-5.0.0.zip", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/type/4.0.0/sebastian-type-4.0.0.zip", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/version/4.0.1/sebastian-version-4.0.1.zip", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "time": "2023-02-07T11:34:05+00:00"}, {"name": "swoole/ide-helper", "version": "6.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/swoole/ide-helper/6.0.2/swoole-ide-helper-6.0.2.zip", "reference": "6f12243dce071714c5febe059578d909698f9a52", "shasum": ""}, "type": "library", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "support": {"issues": "https://github.com/swoole/ide-helper/issues", "source": "https://github.com/swoole/ide-helper/tree/6.0.2"}, "time": "2025-03-23T07:31:41+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/event-dispatcher/v6.4.13/symfony-event-dispatcher-v6.4.13.zip", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/event-dispatcher-contracts/v3.5.1/symfony-event-dispatcher-contracts-v3.5.1.zip", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.13", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/filesystem/v6.4.13/symfony-filesystem-v6.4.13.zip", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "time": "2024-10-25T15:07:50+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.21", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/http-foundation/v6.4.21/symfony-http-foundation-v6.4.21.zip", "reference": "3f0c7ea41db479383b81d436b836d37168fd5b99", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.21"}, "time": "2025-04-27T13:27:38+00:00"}, {"name": "symfony/options-resolver", "version": "v6.4.16", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/options-resolver/v6.4.16/symfony-options-resolver-v6.4.16.zip", "reference": "368128ad168f20e22c32159b9f761e456cec0c78", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v6.4.16"}, "time": "2024-11-20T10:57:02+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/polyfill-php81/v1.32.0/symfony-polyfill-php81-v1.32.0.zip", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-php83/v1.32.0/symfony-polyfill-php83-v1.32.0.zip", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v6.4.20", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/process/v6.4.20/symfony-process-v6.4.20.zip", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.20"}, "time": "2025-03-10T17:11:00+00:00"}, {"name": "symfony/stopwatch", "version": "v6.4.19", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/stopwatch/v6.4.19/symfony-stopwatch-v6.4.19.zip", "reference": "dfe1481c12c06266d0c3d58c0cb4b09bd497ab9c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v6.4.19"}, "time": "2025-02-21T10:06:30+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/theseer/tokenizer/1.2.3/theseer-tokenizer-1.2.3.zip", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.1", "ext-fileinfo": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-redis": "*", "ext-swoole": ">=5.0"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}