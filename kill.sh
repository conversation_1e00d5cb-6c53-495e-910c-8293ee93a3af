#!/bin/bash

PORTS=(9501 9502 9503)

#echo "正在查找并终止使用以下端口的进程: ${PORTS[*]}"

for port in "${PORTS[@]}"
do
  echo "正在检查端口: $port"
  # 查找使用指定端口的进程ID (PID)
  # 对于 Linux 系统:
  PIDS=$(netstat -tulnp | grep ":$port " | awk '{print $7}' | cut -d'/' -f1)
  # 对于 macOS 系统 (如果 netstat 不适用，可以尝试 lsof):
  # PIDS=$(lsof -ti tcp:"$port")

  if [ -z "$PIDS" ]; then
    echo "端口 $port 没有找到正在运行的进程。"
  else
    for PID in $PIDS
    do
      if [[ "$PID" =~ ^[0-9]+$ ]]; then # 确保 PID 是一个数字
        echo "找到进程 PID: $PID 在端口 $port 上。正在终止..."
        kill -9 "$PID" # 使用 kill -9 强制终止进程
        if [ $? -eq 0 ]; then
          echo "进程 PID: $PID 已成功终止。"
        else
          echo "终止进程 PID: $PID 失败。"
        fi
      else
        echo "在端口 $port 上找到无效的 PID: '$PID'"
      fi
    done
  fi
#  echo "---"
done

#echo "脚本执行完毕。"